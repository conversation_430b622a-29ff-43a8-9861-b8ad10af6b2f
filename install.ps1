# PyMuPDF4LLM MCP 安装脚本
# 使用国内镜像源加速安装

Write-Host "开始安装 PyMuPDF4LLM MCP 服务..." -ForegroundColor Green

# 设置国内镜像源
$env:UV_INDEX_URL = "https://pypi.tuna.tsinghua.edu.cn/simple"
$env:Path = "C:\Users\<USER>\.local\bin;$env:Path"

Write-Host "使用清华大学镜像源: $env:UV_INDEX_URL" -ForegroundColor Yellow

# 进入项目目录
Set-Location "c:\Users\<USER>\Desktop\整理\pymupdf4llm-mcp-main"

Write-Host "当前目录: $(Get-Location)" -ForegroundColor Blue

# 方法1: 尝试创建虚拟环境并安装
Write-Host "方法1: 尝试使用 uv 创建虚拟环境..." -ForegroundColor Cyan
try {
    uv venv --python 3.10
    if ($LASTEXITCODE -eq 0) {
        Write-Host "虚拟环境创建成功，开始安装依赖..." -ForegroundColor Green
        uv pip install -e .
        if ($LASTEXITCODE -eq 0) {
            Write-Host "安装成功！" -ForegroundColor Green
            Write-Host "可以使用以下命令运行MCP服务:" -ForegroundColor Yellow
            Write-Host "uv run pymupdf4llm-mcp stdio" -ForegroundColor White
            exit 0
        }
    }
} catch {
    Write-Host "方法1失败，尝试方法2..." -ForegroundColor Red
}

# 方法2: 使用系统Python (如果版本合适)
Write-Host "方法2: 检查系统Python版本..." -ForegroundColor Cyan
$pythonVersion = python --version 2>&1
Write-Host "系统Python版本: $pythonVersion" -ForegroundColor Blue

if ($pythonVersion -match "Python 3\.1[0-9]" -or $pythonVersion -match "Python 3\.[2-9][0-9]") {
    Write-Host "Python版本符合要求，使用系统Python安装..." -ForegroundColor Green
    try {
        uv pip install --system -e .
        if ($LASTEXITCODE -eq 0) {
            Write-Host "安装成功！" -ForegroundColor Green
            Write-Host "可以使用以下命令运行MCP服务:" -ForegroundColor Yellow
            Write-Host "python -m pymupdf4llm_mcp.cli stdio" -ForegroundColor White
            exit 0
        }
    } catch {
        Write-Host "系统Python安装失败" -ForegroundColor Red
    }
} else {
    Write-Host "系统Python版本不符合要求 (需要 >= 3.10)" -ForegroundColor Red
}

# 方法3: 手动安装依赖
Write-Host "方法3: 尝试手动安装依赖..." -ForegroundColor Cyan
Write-Host "请手动执行以下步骤:" -ForegroundColor Yellow
Write-Host "1. 安装 Python 3.10 或更高版本" -ForegroundColor White
Write-Host "2. 使用 pip 安装依赖:" -ForegroundColor White
Write-Host "   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple mcp[cli] pydantic pymupdf4llm typer" -ForegroundColor Gray
Write-Host "3. 设置 PYTHONPATH:" -ForegroundColor White
Write-Host "   `$env:PYTHONPATH = 'c:\Users\<USER>\Desktop\整理\pymupdf4llm-mcp-main'" -ForegroundColor Gray
Write-Host "4. 运行服务:" -ForegroundColor White
Write-Host "   python -m pymupdf4llm_mcp.cli stdio" -ForegroundColor Gray

Write-Host "`n配置文件已生成: mcp-config.json" -ForegroundColor Green
Write-Host "您可以将此配置添加到您的MCP客户端中" -ForegroundColor Green
