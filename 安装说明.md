# PyMuPDF4LLM MCP 服务安装说明

## 前提条件
- Python 3.10 或更高版本
- 网络连接（用于下载依赖包）

## 安装方法

### 方法1: 使用提供的安装脚本
```powershell
.\install.ps1
```

### 方法2: 手动安装

#### 1. 安装Python依赖
使用国内镜像源加速安装：

```powershell
# 使用清华大学镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple mcp[cli] pydantic pymupdf4llm typer

# 或使用阿里云镜像源
pip install -i https://mirrors.aliyun.com/pypi/simple mcp[cli] pydantic pymupdf4llm typer

# 或使用中科大镜像源
pip install -i https://pypi.mirrors.ustc.edu.cn/simple mcp[cli] pydantic pymupdf4llm typer
```

#### 2. 设置环境变量
```powershell
$env:PYTHONPATH = "c:\Users\<USER>\Desktop\整理\pymupdf4llm-mcp-main"
```

#### 3. 测试运行
```powershell
cd "c:\Users\<USER>\Desktop\整理\pymupdf4llm-mcp-main"
python -m pymupdf4llm_mcp.cli --help
```

#### 4. 启动MCP服务
```powershell
python -m pymupdf4llm_mcp.cli stdio
```

## MCP客户端配置

### Cursor/Windsurf 配置
将以下配置添加到您的MCP客户端配置文件中：

```json
{
  "mcpServers": {
    "pymupdf4llm-mcp": {
      "command": "python",
      "args": [
        "-m", "pymupdf4llm_mcp.cli",
        "stdio"
      ],
      "cwd": "c:\\Users\\<USER>\\Desktop\\整理\\pymupdf4llm-mcp-main",
      "env": {
        "PYTHONPATH": "c:\\Users\\<USER>\\Desktop\\整理\\pymupdf4llm-mcp-main"
      }
    }
  }
}
```

### 使用uv的配置（如果uv可用）
```json
{
  "mcpServers": {
    "pymupdf4llm-mcp": {
      "command": "uv",
      "args": [
        "run",
        "pymupdf4llm-mcp",
        "stdio"
      ],
      "cwd": "c:\\Users\\<USER>\\Desktop\\整理\\pymupdf4llm-mcp-main"
    }
  }
}
```

## 功能说明
这个MCP服务提供以下功能：
- 将PDF文件转换为Markdown格式
- 优化文本提取，适合LLM处理
- 保持文档结构和格式

## 使用示例
一旦配置完成，您就可以在支持MCP的客户端中使用此服务来处理PDF文件了。

## 故障排除

### 常见问题
1. **Python版本不兼容**: 确保使用Python 3.10或更高版本
2. **依赖安装失败**: 尝试使用不同的镜像源
3. **路径问题**: 确保所有路径都使用绝对路径

### 检查安装
```powershell
# 检查Python版本
python --version

# 检查依赖是否安装
python -c "import mcp, pydantic, pymupdf4llm, typer; print('所有依赖已安装')"

# 测试模块导入
python -c "from pymupdf4llm_mcp import app; print('模块导入成功')"
```

## 国内镜像源列表
- 清华大学: https://pypi.tuna.tsinghua.edu.cn/simple
- 阿里云: https://mirrors.aliyun.com/pypi/simple  
- 中科大: https://pypi.mirrors.ustc.edu.cn/simple
- 豆瓣: https://pypi.douban.com/simple
