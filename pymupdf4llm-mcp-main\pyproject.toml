[project]
name = "pymupdf4llm-mcp"
version = "0.0.1"
description = "MCP Server for pymupdf4llm"
authors = [{ name = "Wh1isper", email = "<EMAIL>" }]
readme = "README.md"
keywords = ['python']
requires-python = ">=3.10,<4.0"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "mcp[cli]>=1.6.0",
    "pydantic>=2.11.3",
    "pymupdf4llm>=0.0.21",
    "typer>=0.15.2",
]

[project.urls]
Homepage = "https://github.com/pymupdf/pymupdf4llm-mcp/"
Repository = "https://github.com/pymupdf/pymupdf4llm-mcp/"
Documentation = "https://pymupdf.readthedocs.io/en/latest/pymupdf4llm/"

[dependency-groups]
dev = [
    "pytest>=7.2.0",
    "pre-commit>=2.20.0",
    "pytest-asyncio>=0.25.3",
    "tox-uv>=1.11.3",
    "deptry>=0.22.0",
    "pytest-cov>=4.0.0",
    "ruff>=0.9.2",
    "inline-snapshot",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.setuptools]
py-modules = ["pymupdf4llm_mcp"]

[tool.pytest.ini_options]
testpaths = ["tests"]

[tool.ruff]
target-version = "py39"
line-length = 120
fix = true

[tool.ruff.lint]
select = [
    # flake8-2020
    "YTT",
    # flake8-bandit
    "S",
    # flake8-bugbear
    "B",
    # flake8-builtins
    "A",
    # flake8-comprehensions
    "C4",
    # flake8-debugger
    "T10",
    # flake8-simplify
    "SIM",
    # isort
    "I",
    # mccabe
    "C90",
    # pycodestyle
    "E",
    "W",
    # pyflakes
    "F",
    # pygrep-hooks
    "PGH",
    # pyupgrade
    "UP",
    # ruff
    "RUF",
    # tryceratops
    "TRY",
]
ignore = [
    # LineTooLong
    "E501",
    # DoNotAssignLambda
    "E731",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]

[tool.ruff.format]
preview = true

[tool.coverage.report]
skip_empty = true

[tool.coverage.run]
branch = true
source = ["pymupdf4llm_mcp"]
omit = ["tests/*", "pymupdf4llm_mcp/cli.py"]

[project.scripts]
pymupdf4llm-mcp = "pymupdf4llm_mcp.cli:app"
